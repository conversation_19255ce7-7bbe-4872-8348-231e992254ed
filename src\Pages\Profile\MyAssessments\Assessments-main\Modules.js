import React, { useEffect, useMemo, useRef, useState } from "react";
import Preview from "../../../../Assets/preview.png";
import Klose from "../../../../Assets/klose.png";
import { useQueryClient } from "@tanstack/react-query";
import { getModules, getCompanyModules } from "./hooks/getModules";
import { getModuleByID } from "./hooks/getModuleByID";
import Loader from "react-loader-spinner";
import "../../../../Components/Loading/Loading8.css";
import { useMutation } from "@tanstack/react-query";
import { updateStep } from "./hooks/updateStep";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import ReactHtmlParser from "react-html-parser";
import styles from "./styling2.module.css";
import { useNavigate } from "react-router-dom";
import queryString from "query-string";
import "./invite.css";
import { useSelector } from "react-redux";
import King from "../../../../Assets/preee.png";
import { useDispatch } from "react-redux";
import { setNextModuleToFalse } from "../../../../redux/reducers/NextModules/NextModulesSlice";
import SearhBar from "../../../../Dexta_assets/searchBar.png";
import info from "../../../../Dexta_assets/helpIcon.png";
import clock from "../../../../Dexta_assets/clock.png";
import remove from "../../../../Dexta_assets/close_icon.png";
import { Scrollbars } from "react-custom-scrollbars";
import PremiumGeneral from "../../../../Components/Modals/PremiumGeneral";
import d1 from "../../../../Dexta_assets/d1.png";
import d2 from "../../../../Dexta_assets/d2.png";
import d3 from "../../../../Dexta_assets/d3.png";
import d4 from "../../../../Dexta_assets/d4.png";
import close from "../../../../Dexta_assets/closeModal.png";
import { IoAddCircleOutline, IoTrashBin } from "react-icons/io5";
import { FaRegEye } from "react-icons/fa";
import { setBackModuleToFalse } from "../../../../redux/reducers/BackModule/BackModuleSlice";
import { toast, ToastContainer, Zoom } from "react-toastify";
import { useTranslation } from "react-i18next";
import { getCompanyDetails } from "../../Settings/hooks/getCompanyDetails";

const Modules = (props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [errorMessage, setErrorMessage] = useState("");
  const [error, setError] = useState(false);
  const [sectionID, setSectionID] = useState(0);
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const assessment_id = localStorage.getItem("assessment_ID");
  const userID = localStorage.getItem("CP-USER-ID");
  const [searchedValue, setSearchedValue] = useState("");
  const [searchData, setSearchData] = useState("");
  const navigate = useNavigate();
  const levels = ["entry", "mid", "senior"];
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const [unmatchedPackageCodes, setUnmatchedPackageCodes] = useState([]);
  const [premiumGeneralOpen, setPremiumGeneral] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [staticLoad, setStaticLoad] = useState(false);
  const next = useSelector((state) => state.nextModule.setNextModule);
  const back = useSelector((state) => state.backModule.back);
  const [testType, setTestType] = useState("suggested");
  const [allData, setAllData] = useState([]);
  const listInnerRef = useRef(null);
  const didMountNextRef = useRef(false);
  const didMountBackRef = useRef(false);
  const [paginationInfo, setPaginationInfo] = useState({
    currentTake: 1,
    hasNextPage: true,
  });
  const [currentTake, setCurrentTake] = useState(1);
  const [load, setLoad] = useState(false);
  const [tempSearch, setTempSearch] = useState("");
  const language = localStorage.getItem("i18nextLng");


  //#region search function on job experience
  useEffect(() => {
    if (props.data.experience != "all") {
      const newLevel = levels
        .filter((i) => i != props.data.experience)
        .toString();
      setSearchData(newLevel);
    } else {
      setSearchData("");
    }
  }, [props.data.experience]);
  //#endregion

  //#region calling api to update step
  const { mutate, isLoading: mutateLoad } = useMutation(updateStep, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("assessment");
      dispatch(setNextModuleToFalse(false));
      dispatch(setBackModuleToFalse(false));
      props.setModulesLoading(false);
    },
    onError: (error) => {
      setError(true);
      setErrorMessage(error.response.data.message[0]);
    },
  });

  const { mutate: backMutate, isLoading: backLoad } = useMutation(updateStep, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("assessment");
      props.setselecteditem("general");
      localStorage.setItem("current_module", "general");
      dispatch(setBackModuleToFalse(false));
    },
    onError: (error) => {
      dispatch(setBackModuleToFalse(false));
      props.setselecteditem("general");
      localStorage.setItem("current_module", "general");
    },
  });

  //#endregion

  //#region preview function
  const handleButtonClickPreview = (id) => {
    const url = `/preview-module/${id}`;
    window.open(url, "_blank"); // This opens the link in a new tab
  };
  //#endregion

  //#region function to add module
  const handleAddModule = (id, name, time) => {
    if (props.data.selectedModules.some((m) => m.id === id)) return;
    const newModules = [
      ...props.data.selectedModules,
      { id, name, time: parseInt(time) || 0 },
    ];
    props.setData({
      ...props.data,
      selectedModules: newModules,
      timeMod: recalcTimeMod(newModules),
    });
    setTimeout(() => {
      setStaticLoad(false);
    }, 1000);
  };
  //#endregion

  //#region next function to proceed to next step
  const handleNext = () => {
    props.setselecteditem("questions");
    localStorage.setItem("current_module", "questions");
    props.setModulesLoading(true);

    // Build sections only when there is at least one section to send
    const selectedSectionIds = (props.data?.selectedModules || [])
      .map((m) => m.id)
      .filter(Boolean);
    if (props.data?.customQuestion) {
      selectedSectionIds.push(props.data.customQuestion);
    }

    const content = {
      user: parseInt(localStorage.getItem("CP-USER-ID")),
      status: "draft",
      setpTwo: "completed",
      notes: "very nice assessment",
    };
    if (selectedSectionIds.length > 0) {
      content.sections = selectedSectionIds;
    }

    let data = {
      content,
      categoryID:
        parsed.assessment_id != null
          ? parsed.assessment_id
          : parseInt(assessment_id),
    };
    try {
      mutate(data, props.data.categoryID);
    } catch (err) {
      //
    }
  };

  const handleBack = () => {
    let data = {
      content: {
        user: parseInt(localStorage.getItem("CP-USER-ID")),
        sections: props.data?.customQuestion
          ? [
            ...props.data.selectedModules.map((m) => m.id),
            props.data?.customQuestion,
          ]
          : props.data.selectedModules.map((m) => m.id),
        setpTwo: "completed",
        notes: "very nice assessment",
      },
      categoryID:
        parsed.assessment_id != null
          ? parsed.assessment_id
          : parseInt(assessment_id),
    };
    try {
      backMutate(data, props.data.categoryID);
    } catch (err) {
      //
    }
  };
  useEffect(() => {
    // On first render only: if next was already true from a previous step, reset and skip
    if (!didMountNextRef.current) {
      didMountNextRef.current = true;
      if (next) dispatch(setNextModuleToFalse(false));
      return;
    }
    if (next) {
      handleNext();
    }
  }, [next]);

  useEffect(() => {
    if (!didMountBackRef.current) {
      didMountBackRef.current = true;
      if (back) dispatch(setBackModuleToFalse(false));
      return;
    }
    if (back) {
      handleBack();
    }
  }, [back]);
  //#endregion

  // Reset navigation flags on mount to avoid unintended transitions when landing
  useEffect(() => {
    dispatch(setNextModuleToFalse(false));
    dispatch(setBackModuleToFalse(false));
  }, []);

  // Reset search value when switching between tabs
  useEffect(() => {
    setSearchedValue("");
  }, [testType]);

  //#region adding module ids and names to props
  const handleModulesIDS = (id) => {
    if (props.data.selectedModules.some((m) => m.id === id)) {
      return;
    }
    const newProps = { ...props.data };
    const newModuleID = newProps.selectedModules.map((m) => m.id);
    newModuleID.push(id);
    newProps["selectedModules"] = [
      ...newProps.selectedModules,
      { id, name: "", time: 0 },
    ];
    props.setData(newProps);
  };
  const handleAddModuleNamee = (name, time = 0) => {
    if (props.data.selectedModules.some((m) => m.name === name)) {
      return;
    }
    props.setData({
      ...props.data,
      selectedModules: [
        ...props.data.selectedModules,
        { id: "", name, time: parseInt(time) || 0 },
      ],
      timeMod: props.data.timeMod + parseInt(time),
    });
  };
  //#endregion

  //#region Function to delete module ID and Name
  const handleDeleteModule = (id) => {
    if (props.data.selectedModules.some((m) => m.id === id)) {
      const newModules = props.data.selectedModules.filter((m) => m.id !== id);
      props.setData({
        ...props.data,
        selectedModules: newModules,
        timeMod: recalcTimeMod(newModules),
      });
    }
  };

  //#endregion

  //#region fetching modules data
  const {
    data,
    error: errorD,
    isLoading,
  } = useQuery(
    [
      "sections",
      props.data.experience,
      props.data.job,
      searchedValue,
      paginationInfo.currentTake,
      language,
    ],
    () =>
      getModules(
        props.data.experience,
        props.data.job,
        searchedValue,
        paginationInfo.currentTake,
        language
      ),
    {
      keepPreviousData: true,
    }
  );

  const {
    data: data_section,
    error: error_section,
    isLoading: loading_section,
  } = useQuery(
    [
      "sections",
      props.data.experience,
      props.data.job,
      tempSearch,
      paginationInfo.currentTake,
      language,
    ],
    () =>
      getModules(
        props.data.experience,
        props.data.job,
        tempSearch,
        paginationInfo.currentTake,
        language
      )
  );

  const {
    data: companyData,
    error: companyError,
    isLoading: companyLoading,
  } = useQuery(
    [
      "company-sections",
      props.data.experience,
      props.data.job,
      searchedValue,
      paginationInfo.currentTake,
      language,
    ],
    () =>
      getCompanyModules(
        props.data.experience,
        props.data.job,
        searchedValue,
        paginationInfo.currentTake,
        language
      ),
    {
      keepPreviousData: true,
      enabled: testType === "company",
    }
  );


  function handleButtonClick(event) {
    event.stopPropagation();
  }

  const {
    data: ModuleData,
    error: errorModule,
    isLoading: isLoadingModule,
  } = useQuery(["sections", sectionID, language], () =>
    getModuleByID(sectionID, language)
  );
  //#endregion

  //#region pagination on scroll
  const onScroll = () => {
    if (listInnerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = listInnerRef.current;
      if (scrollTop + clientHeight === scrollHeight) {
        if (data?.meta?.hasNextPage !== false) {
          setLoad(true);
          setPaginationInfo((prevPaginationInfo) => ({
            ...prevPaginationInfo,
            currentTake: prevPaginationInfo.currentTake + 1,
          }));
          setCurrentTake(currentTake + 1);
          setTimeout(() => {
            setLoad(false);
          }, 1000);
        }
      }
    }
  };

  useEffect(() => {
    if (!isLoading && data && paginationInfo.currentTake === 1) {
      setAllData(data?.data?.relatedData);
    } else if (!isLoading && data && paginationInfo.currentTake > 1) {
      setAllData((prevData) => {
        const newData = data?.data?.relatedData || [];
        const filteredData = newData.filter(
          (newItem) => !prevData.some((prevItem) => prevItem.id === newItem.id)
        );
        return [...prevData, ...filteredData];
      });
    }
  }, [data, isLoading, paginationInfo.currentTake]);

  useEffect(() => {
    if (!isLoading) {
      setPaginationInfo({
        currentTake: 1,
        hasNextPage: true,
      });
      setAllData(data?.data?.relatedData || []);
    }
  }, [isLoading, searchedValue]);

  //#endregion

  const handleEmailClick = () => {
    //     const recipient = "<EMAIL>";
    //     const subject = "Request of new module";
    //     const body = `Hi Dexta team,

    // Can you please create a new module which covers [add module details]. This module is for the role [add role name].

    // Thanks`;

    //     const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(
    //       subject
    //     )}&body=${encodeURIComponent(body)}`;

    window.open("https://dexta.io/contact", "_blank");
  };

  // Helper: get selectedModules from props.data, fallback to []
  const selectedModules = props.data.selectedModules || [];

  // Helper: recalculate timeMod from selectedModules
  const recalcTimeMod = (modules) =>
    modules.reduce((sum, m) => sum + (parseInt(m.time) || 0), 0);

  // For regular modules sidebar - show only regular modules (not custom)
  const regularModulesOnly = selectedModules.filter(module => !module.isCustom);
  const MAX_REGULAR_MODULES = 5;

  const customModulesCount = selectedModules.filter(module => module.isCustom).length;
  const totalModulesCount = selectedModules.length;
  const TOTAL_MODULE_LIMIT = 6;

  // Calculate how many regular modules can still be added
  const availableSlotsForRegular = Math.max(0, TOTAL_MODULE_LIMIT - totalModulesCount);
  const canAddMoreRegularModules = regularModulesOnly.length < availableSlotsForRegular;


  //#region getting Company Details
  const { data: companyDetails, isLoading: companyloading } = useQuery(
    ["company", userID],
    () => getCompanyDetails(userID)
  );

  //#endregion

  return (
    <div>
      <PremiumGeneral
        premiumGeneralOpen={premiumGeneralOpen}
        setPremiumGeneral={setPremiumGeneral}
      />
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        enableMultiContainer={false}
        limit={1}
      />
      <div className="sm:px-0 md:px-2">
        <div className="bg-white grid sm:grid-cols-1 lg:grid-cols-12 md:p-6">
          <div className="col-span-3 sm:border-none lg:border-r sm:p-6 md:p-0">
            <h2
              className="text-coalColor mt-3"
              style={{ fontFamily: "Archia Semibold" }}
            >
              {t("create_test.modules.add_modules_section.title")}
            </h2>
            <p
              className="mt-5 text-sm w-4/5"
              style={{ fontFamily: "Silka Light" }}
            >
              {t("create_test.modules.add_modules_section.description")}
            </p>
            <p className="mt-8 text-sm" style={{ fontFamily: "Silka Light" }}>
              {t("create_test.modules.add_modules_section.you_have_added")}
            </p>
            <div className="flex mt-5 gap-3 flex-col w-full">
              {regularModulesOnly.map((mod, index) => (
                <div
                  className="sm:pr-0 lg:pr-5"
                  style={{ fontFamily: "Archia Semibold" }}
                  key={mod.id}
                >
                  <button
                    type="button"
                    className={`text-coalColor relative flex good4 border border-[#252E3A] bg-[#C0FF06] focus:outline-none font-bold rounded-lg text-sm w-full align-center py-4 text-left overflow-hidden`}
                    onMouseEnter={() => setHoveredIndex(index)}
                    onMouseLeave={() => setHoveredIndex(null)}
                    title={mod.name}
                  >
                    <div className="flex items-center justify-center w-full relative">
                      <span className={`truncate px-5 w-5/6 mr-auto`}>
                        {mod.name || `Module ${index + 1}`}
                      </span>
                      <div className="absolute top-0 right-2 ml-4 bottom-0 flex items-center justify-center pr-2">
                        <img
                          src={remove}
                          alt="Cross Icon"
                          className="w-5 h-5"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteModule(mod.id);
                          }}
                        />
                      </div>
                    </div>
                  </button>
                </div>
              ))}
              {[...Array(Math.max(MAX_REGULAR_MODULES - regularModulesOnly.length, 0))].map(
                (_, index) => (
                  <div
                    className="sm:pr-0 lg:pr-5"
                    style={{ fontFamily: "Archia Semibold" }}
                    key={regularModulesOnly.length + index}
                  >
                    <button
                      type="button"
                      className={`text-[#7C8289] flex good4 border-2 border-[#D3D5D8] bg-[#F6F7F7] px-4 focus:outline-none rounded-lg text-sm w-full align-center py-4 justify-center text-left`}
                    >
                      {`${t(
                        "create_test.modules.add_modules_section.select_module"
                      )} ${regularModulesOnly.length + index + 1}`}
                    </button>
                  </div>
                )
              )}
            </div>
          </div>
          <div className="col-span-9 lg:px-4">
            <div className="flex lg:justify-between sm:mt-10 lg:mt-0 lg:flex-row sm:flex-col">
              <div className="flex flex-wrap gap-2 sm:gap-3 lg:gap-5 px-2 my-auto">
                <h2
                  className={`${testType === "suggested"
                      ? "bg-coalColor text-primaryGreen rounded-md"
                      : "text-[#7C8289]"
                    } sm:p-2 md:p-3 cursor-pointer text-sm sm:text-base whitespace-nowrap`}
                  onClick={() => {
                    if (testType !== "suggested") {
                      setTestType("suggested");
                    }
                  }}
                  style={{ fontFamily: "Archia Semibold" }}
                >
                  {t("create_test.modules.suggested_modules")}
                </h2>
                <h2
                  className={`${testType === "other"
                      ? "bg-coalColor text-primaryGreen rounded-md"
                      : "text-[#7C8289]"
                    } sm:p-2 md:p-3 cursor-pointer text-sm sm:text-base whitespace-nowrap`}
                  style={{ fontFamily: "Archia Semibold" }}
                  onClick={() => {
                    if (testType !== "other") {
                      setTestType("other");
                    }
                  }}
                >
                  {t("create_test.modules.all_modules")}
                </h2>
                <h2
                  className={`${testType === "company"
                      ? "bg-coalColor text-primaryGreen rounded-md"
                      : "text-[#7C8289]"
                    } sm:p-2 md:p-3 cursor-pointer text-sm sm:text-base flex-shrink-0`}
                  style={{ fontFamily: "Archia Semibold" }}
                  onClick={() => {
                    if (testType !== "company") {
                      setTestType("company");
                    }
                  }}
                >
                  <span className="sm:hidden">
                    {t("create_test.modules.company_modules")}
                  </span>
                  <span className="hidden sm:inline">
                    {companyDetails?.data[0]?.companyName}'s{" "}
                    {t("create_test.modules.company_modules")}
                  </span>
                </h2>
              </div>
              <div className="relative sm:mt-4 lg:mt-0 sm:w-full sm:px-4 md:px-0 xl:w-1/3 2xl:w-1/4">
                <div className="absolute inset-y-0 left-0 flex items-center sm:pl-6 md:pl-3 pointer-events-none">
                  <img src={SearhBar} className="md:w-5 md:h-5 sm:w-4 sm:h-4" />
                </div>
                <input
                  type="text"
                  className="bg-gray-50 border focus:ring-0 border-gray-300 text-gray-900 text-sm rounded block w-full pl-10 p-3 px-10"
                  placeholder={t("create_test.modules.search_placeholder")}
                  required
                  value={searchedValue}
                  onChange={(e) => setSearchedValue(e.target.value)}
                />
                <div className="flex items-center inset-y-0 right-0 absolute sm:pr-3 md:pr-1">
                  <div className="group relative left-10 m-12 flex justify-center">
                    <img
                      src={info}
                      data-tooltip-target="tooltip-default"
                      className="w-5 h-5 cursor-pointer"
                    />
                    <span className="absolute scale-0 right-0 bottom-11 rounded bg-gray-800 left-50 p-2 text-xs w-[20rem] text-white group-hover:scale-100">
                      {t("create_test.modules.search_tooltip")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {isLoading || (testType === "company" && companyLoading) ? (
              <div class="loader-container-1">
                <div class="loader-1"></div>
              </div>
            ) : (
              <React.Fragment>
                {testType === "suggested" ? (
                  <>
                    <div
                      className={`mt-5 mb-5 ${!data?.data?.relatedData?.length == 0 && "h-[100vh]"
                        } enable-scrollbar2`}
                    >
                      <Scrollbars
                        autoHide
                        style={{ width: "100%", height: "90%" }}
                      >
                        <div
                          className="lg:grid-cols-2 xl:grid-cols-2 px-4 lg:pr-4 2xl:grid-cols-3 gap-4 md:grid-cols-2 grid sm:grid-cols-1"
                          ref={listInnerRef}
                          onScroll={onScroll}
                        >
                          {data?.data?.relatedData
                            ?.slice()
                            ?.sort((a, b) => a.name.localeCompare(b.name))
                            .map((i, j) => {
                              return (
                                <React.Fragment key={j}>
                                  {(i.status === "active" ||
                                    i?.status === "coming_soon") && (
                                      <div className="" key={j}>
                                        <div className="py-2">
                                          <div
                                            className={`bg-[#F6F7F7] px-5 border relative border-[#D3D5D8] corner-ribbon p-3 rounded-lg`}
                                          >
                                            {i?.status === "coming_soon" && (
                                              <div className="ribbon-container">
                                                <div className="ribbon">
                                                  <span class="ribbon-text">
                                                    {t(
                                                      "create_test.modules.coming_soon"
                                                    )}
                                                  </span>
                                                </div>
                                              </div>
                                            )}
                                            <div className="w-full grid grid-cols-10 md:gap-4 h-[80px]">
                                              <div className="col-span-7 my-auto">
                                                <h1
                                                  className={`text-lg font-bold good3 ${i?.status === "coming_soon" &&
                                                    "pl-10"
                                                    }  my-auto`}
                                                  style={{
                                                    fontFamily: "Archia Semibold",
                                                  }}
                                                >
                                                  {i.name}
                                                </h1>
                                              </div>
                                              <div
                                                className="my-auto col-span-3 ml-auto"
                                                style={{ fontFamily: "Silka" }}
                                              >
                                                {i?.experience === "Beginner" && (
                                                  <div className="inline-flex items-center border border-[#0B5B23] text-black py-1 px-3 rounded-full text-[12px]">
                                                    <span className="text-center">
                                                      {t(
                                                        "create_test.modules.experience_levels.beginner"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience ===
                                                  "Intermediate" && (
                                                    <div className="inline-flex items-center border border-[#FFB500] text-black  w-[90px] lg:w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                      <span className="text-center mx-auto">
                                                        {t(
                                                          "create_test.modules.experience_levels.intermediate"
                                                        )}
                                                      </span>
                                                    </div>
                                                  )}
                                                {i?.experience === "Advanced" && (
                                                  <div className="inline-flex items-center border border-[#FF5812] text-black  w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.advanced"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience === "General" && (
                                                  <div className="inline-flex items-center border border-coalColor text-black  xl:w-[72px] 2xl:w-[90px] md:w-[100px] py-1 rounded-full text-[12px] sm:w-[84px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.all_levels"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className="flex flex-row gap-2 mt-5">
                                              <img
                                                src={clock}
                                                className="w-4 h-4"
                                              />
                                              <p
                                                className="text-xs text-coalColor"
                                                style={{
                                                  fontFamily: "Silka Light",
                                                }}
                                              >
                                                {i?.time}{" "}
                                                {t("create_test.modules.mins")}
                                              </p>
                                            </div>
                                            <div className="h-[90px] pr-3 text-sm 2xl:mt-3 sm:mt-3">
                                              <div className="iosClamping h-[80px] overflow-hidden">
                                                <div
                                                  className={
                                                    styles["html-content"]
                                                  }
                                                >
                                                  {ReactHtmlParser(i.notes)}
                                                </div>
                                              </div>
                                              {i.notes.length > 100 && (
                                                <div
                                                  className="underline"
                                                  style={{
                                                    color: "#252E3A",
                                                    cursor: "pointer",
                                                    fontFamily: "Silka",
                                                  }}
                                                  onClick={() => {
                                                    setSectionID(i.id);
                                                    setDropdownOpen(true);
                                                  }}
                                                >
                                                  {" "}
                                                  {t(
                                                    "create_test.modules.read_more"
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                            <div className="grid grid-cols-2 mt-5">
                                              <div className="my-auto">
                                                <a
                                                  className="text-sm underline"
                                                  style={{ fontFamily: "Silka" }}
                                                  href={`/preview-module/${i.id}`}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                >
                                                  {t(
                                                    "create_test.modules.sample_questions"
                                                  )}
                                                </a>{" "}
                                              </div>
                                              <div>
                                                {selectedModules.some(
                                                  (m) => m.id === i.id
                                                ) ? (
                                                  <button
                                                    className="inline-flex items-center w-full px-4 py-3 justify-center border border-coalColor bg-[#C0FF06] text-coalColor text-sm font-bold rounded-md"
                                                    style={{
                                                      fontFamily: "Silka",
                                                    }}
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      handleDeleteModule(i.id);
                                                    }}
                                                  >
                                                    {t(
                                                      "create_test.modules.remove"
                                                    )}
                                                  </button>
                                                ) : (
                                                  <div className="relative">
                                                    {i?.packages &&
                                                      i?.packages.length > 0 ? (
                                                      i?.packages.filter(
                                                        (jj) =>
                                                          jj.code ===
                                                          user_package_check
                                                      ).length > 0 ||
                                                        i?.packages.filter(
                                                          (jj) => jj.code === "free"
                                                        ).length > 0 ? (
                                                        // First condition: user_package_check is present in packages array
                                                        <button
                                                          className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                              MAX_REGULAR_MODULES ||
                                                              i?.status ===
                                                              "coming_soon" || availableSlotsForRegular === 0
                                                              ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                              : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                            } text-sm font-medium rounded-md relative`}
                                                          disabled={
                                                            regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES ||
                                                            i?.status ===
                                                            "coming_soon" || availableSlotsForRegular === 0
                                                          }
                                                          onClick={(e) => {
                                                            e.stopPropagation();
                                                            setStaticLoad(true);
                                                            handleAddModule(
                                                              i.id,
                                                              i.name,
                                                              i.time
                                                            );
                                                          }}
                                                          style={{
                                                            fontFamily:
                                                              "Archia Semibold",
                                                          }}
                                                        >
                                                          {false ? (
                                                            <span className="flex items-center justify-center">
                                                              <Loader
                                                                type="Oval"
                                                                color="white"
                                                                height={20}
                                                                width={20}
                                                              />
                                                              <span className="ml-2">
                                                                {t(
                                                                  "create_test.modules.adding"
                                                                )}
                                                              </span>
                                                            </span>
                                                          ) : (
                                                            t(
                                                              "create_test.modules.add_module"
                                                            )
                                                          )}
                                                        </button>
                                                      ) : (
                                                        // Second condition: user_package_check is not present in packages array
                                                        <button
                                                          className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                              MAX_REGULAR_MODULES ||
                                                              i?.status ===
                                                              "coming_soon"
                                                              ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                              : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                            } text-sm font-medium rounded-md relative`}
                                                          disabled={
                                                            regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES ||
                                                            i?.status ===
                                                            "coming_soon"
                                                          }
                                                          onClick={(e) => {
                                                            e.stopPropagation();
                                                            const unmatchedCodes =
                                                              i?.packages
                                                                .filter(
                                                                  (pkg) =>
                                                                    pkg.code !==
                                                                    user_package_check
                                                                )
                                                                .map(
                                                                  (pkg) =>
                                                                    pkg.code
                                                                );
                                                            setUnmatchedPackageCodes(
                                                              (prevCodes) => [
                                                                ...prevCodes,
                                                                ...unmatchedCodes,
                                                              ]
                                                            );
                                                            setPremiumGeneral(
                                                              true
                                                            );
                                                          }}
                                                          style={{
                                                            fontFamily:
                                                              "Archia Semibold",
                                                          }}
                                                        >
                                                          {/* King icon for premium modules */}
                                                          <img
                                                            src={King}
                                                            alt="Premium"
                                                            className="absolute -top-2 -right-2 w-5 h-5 z-10"
                                                          />
                                                          {false ? (
                                                            <span className="flex items-center justify-center">
                                                              <Loader
                                                                type="Oval"
                                                                color="white"
                                                                height={20}
                                                                width={20}
                                                              />
                                                              <span className="ml-2">
                                                                {t(
                                                                  "create_test.modules.adding"
                                                                )}
                                                              </span>
                                                            </span>
                                                          ) : (
                                                            t(
                                                              "create_test.modules.add_module"
                                                            )
                                                          )}
                                                        </button>
                                                      )
                                                    ) : (
                                                      // Third condition: packages array is empty
                                                      <button
                                                        className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES
                                                            ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                            : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                          } text-sm font-medium rounded-md relative`}
                                                        disabled={
                                                          regularModulesOnly.length ===
                                                          MAX_REGULAR_MODULES || availableSlotsForRegular === 0
                                                        }
                                                        onClick={(e) => {
                                                          e.stopPropagation();
                                                          setStaticLoad(true);
                                                          handleAddModule(
                                                            i.id,
                                                            i.name,
                                                            i.time
                                                          );
                                                        }}
                                                        style={{
                                                          fontFamily:
                                                            "Archia Semibold",
                                                        }}
                                                      >
                                                        {false ? (
                                                          <span className="flex items-center justify-center">
                                                            <Loader
                                                              type="Oval"
                                                              color="white"
                                                              height={20}
                                                              width={20}
                                                            />
                                                            <span className="ml-2">
                                                              {t(
                                                                "create_test.modules.adding"
                                                              )}
                                                            </span>
                                                          </span>
                                                        ) : (
                                                          t(
                                                            "create_test.modules.add_module"
                                                          )
                                                        )}
                                                      </button>
                                                    )}
                                                    {regularModulesOnly.length ===
                                                      MAX_REGULAR_MODULES && (
                                                        <div className="tooltip2 w-[180px] text-center">
                                                          {t(
                                                            "create_test.modules.tooltips.cannot_add_more_than_5"
                                                          )}
                                                        </div>
                                                      )}
                                                    {availableSlotsForRegular === 0 && (
                                                      <div className="tooltip2 w-[180px] text-center">
                                                        {t(
                                                          "You have added 6 modules already. You cannot add more modules"
                                                        )}
                                                      </div>
                                                    )}
                                                    {i?.status ===
                                                      "coming_soon" && (
                                                        <div className="tooltip2 w-[180px] text-center">
                                                          {t(
                                                            "create_test.modules.tooltips.cannot_add_this_module"
                                                          )}
                                                        </div>
                                                      )}
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                </React.Fragment>
                              );
                            })}
                        </div>
                      </Scrollbars>
                    </div>
                    {data?.data?.relatedData?.length == 0 && (
                      <div className="px-6 mt-3">
                        <div className="border border-[#FF5812] py-4 rounded">
                          <p
                            className="text-alertRed text-center"
                            style={{ fontFamily: "Silka" }}
                          >
                            {t("create_test.modules.no_modules_found.message")}{" "}
                            {""}
                            <span
                              style={{ fontFamily: "Silka Bold" }}
                              className="font-bold cursor-pointer"
                              onClick={handleEmailClick}
                            >
                              {t(
                                "create_test.modules.no_modules_found.clicking_here"
                              )}
                            </span>
                            .{" "}
                            {t(
                              "create_test.modules.no_modules_found.turnaround_time"
                            )}
                          </p>
                        </div>
                      </div>
                    )}
                  </>
                ) : testType === "other" ? (
                  <>
                    <div
                      className={`mt-5 ${!data?.data?.other?.length == 0 && "h-[100vh]"
                        } mb-5 enable-scrollbar2`}
                    >
                      <Scrollbars
                        autoHide
                        style={{ width: "100%", height: "90%" }}
                      >
                        <div
                          className="lg:grid-cols-2 xl:grid-cols-2 px-4 lg:pr-4 2xl:grid-cols-3 gap-4 md:grid-cols-2 grid sm:grid-cols-1"
                          ref={listInnerRef}
                          onScroll={onScroll}
                        >
                          {data?.data?.other
                            ?.slice()
                            ?.sort((a, b) => a.name.localeCompare(b.name))
                            .map((i, j) => {
                              return (
                                <React.Fragment key={j}>
                                  {(i.status === "active" ||
                                    i?.status === "coming_soon") && (
                                      <div className="" key={j}>
                                        <div className="py-2">
                                          <div
                                            className={`bg-[#F6F7F7] px-5 border relative border-[#D3D5D8] corner-ribbon p-3 rounded-lg`}
                                          >
                                            {i?.status === "coming_soon" && (
                                              <div className="ribbon-container">
                                                <div className="ribbon">
                                                  <span class="ribbon-text">
                                                    {t(
                                                      "create_test.modules.coming_soon"
                                                    )}
                                                  </span>
                                                </div>
                                              </div>
                                            )}
                                            <div className="w-full grid grid-cols-10 md:gap-4 h-[80px]">
                                              <div className="col-span-7 my-auto">
                                                <h1
                                                  className={`text-lg font-bold good3 ${i?.status === "coming_soon" &&
                                                    "pl-10"
                                                    }  my-auto`}
                                                  style={{
                                                    fontFamily: "Archia Semibold",
                                                  }}
                                                >
                                                  {i.name}
                                                </h1>
                                              </div>
                                              <div
                                                className="my-auto col-span-3 ml-auto"
                                                style={{ fontFamily: "Silka" }}
                                              >
                                                {i?.experience === "Beginner" && (
                                                  <div className="inline-flex items-center border border-[#0B5B23] text-black py-1 px-3 rounded-full text-[12px]">
                                                    <span className="text-center">
                                                      {t(
                                                        "create_test.modules.experience_levels.beginner"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience ===
                                                  "Intermediate" && (
                                                    <div className="inline-flex items-center border border-[#FFB500] text-black  w-[90px] lg:w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                      <span className="text-center mx-auto">
                                                        {t(
                                                          "create_test.modules.experience_levels.intermediate"
                                                        )}
                                                      </span>
                                                    </div>
                                                  )}
                                                {i?.experience === "Advanced" && (
                                                  <div className="inline-flex items-center border border-[#FF5812] text-black  w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.advanced"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience === "General" && (
                                                  <div className="inline-flex items-center border border-coalColor text-black  xl:w-[72px] 2xl:w-[90px] md:w-[100px] py-1 rounded-full text-[12px] sm:w-[84px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.all_levels"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className="flex flex-row gap-2 mt-5">
                                              <img
                                                src={clock}
                                                className="w-4 h-4"
                                              />
                                              <p
                                                className="text-xs text-coalColor"
                                                style={{
                                                  fontFamily: "Silka Light",
                                                }}
                                              >
                                                {i?.time}{" "}
                                                {t("create_test.modules.mins")}
                                              </p>
                                            </div>
                                            <div className="h-[90px] pr-3 text-sm 2xl:mt-3 sm:mt-3 ">
                                              <div className="iosClamping h-[80px] overflow-hidden">
                                                <div
                                                  className={
                                                    styles["html-content"]
                                                  }
                                                >
                                                  {ReactHtmlParser(i.notes)}
                                                </div>
                                              </div>
                                              {i.notes.length > 100 && (
                                                <div
                                                  className="underline"
                                                  style={{
                                                    color: "#252E3A",
                                                    cursor: "pointer",
                                                    fontFamily: "Silka",
                                                  }}
                                                  onClick={() => {
                                                    setSectionID(i.id);
                                                    setDropdownOpen(true);
                                                  }}
                                                >
                                                  {" "}
                                                  {t(
                                                    "create_test.modules.read_more"
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                            <div className="grid grid-cols-2 mt-5">
                                              <div className="my-auto">
                                                <a
                                                  className="text-sm underline"
                                                  style={{ fontFamily: "Silka" }}
                                                  href={`/preview-module/${i.id}`}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                >
                                                  {t(
                                                    "create_test.modules.sample_questions"
                                                  )}
                                                </a>{" "}
                                              </div>
                                              <div>
                                                {selectedModules.some(
                                                  (m) => m.id === i.id
                                                ) ? (
                                                  <button
                                                    className="inline-flex items-center w-full  px-4 py-3 justify-center border border-coalColor bg-[#C0FF06] text-coalColor text-sm font-bold rounded-md"
                                                    style={{
                                                      fontFamily: "Silka",
                                                    }}
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      handleDeleteModule(i.id);
                                                    }}
                                                  >
                                                    {t(
                                                      "create_test.modules.remove"
                                                    )}
                                                  </button>
                                                ) : (
                                                  <div className="relative">
                                                    {i?.packages &&
                                                      i?.packages.length > 0 ? (
                                                      i?.packages.filter(
                                                        (jj) =>
                                                          jj.code ===
                                                          user_package_check
                                                      ).length > 0 ||
                                                        i?.packages.filter(
                                                          (jj) => jj.code === "free"
                                                        ).length > 0 ? (
                                                        // First condition: user_package_check is present in packages array
                                                        <button
                                                          className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                              MAX_REGULAR_MODULES ||
                                                              i?.status ===
                                                              "coming_soon"
                                                              ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                              : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                            } text-sm font-medium rounded-md relative`}
                                                          disabled={
                                                            regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES ||
                                                            i?.status ===
                                                            "coming_soon"
                                                          }
                                                          onClick={(e) => {
                                                            e.stopPropagation();
                                                            setStaticLoad(true);
                                                            handleAddModule(
                                                              i.id,
                                                              i.name,
                                                              i.time
                                                            );
                                                          }}
                                                          style={{
                                                            fontFamily:
                                                              "Archia Semibold",
                                                          }}
                                                        >
                                                          {false ? (
                                                            <span className="flex items-center justify-center">
                                                              <Loader
                                                                type="Oval"
                                                                color="white"
                                                                height={20}
                                                                width={20}
                                                              />
                                                              <span className="ml-2">
                                                                {t(
                                                                  "create_test.modules.adding"
                                                                )}
                                                              </span>
                                                            </span>
                                                          ) : (
                                                            t(
                                                              "create_test.modules.add_module"
                                                            )
                                                          )}
                                                        </button>
                                                      ) : (
                                                        // Second condition: user_package_check is not present in packages array
                                                        <button
                                                          className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                              MAX_REGULAR_MODULES ||
                                                              i?.status ===
                                                              "coming_soon"
                                                              ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                              : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                            } text-sm font-medium rounded-md relative`}
                                                          disabled={
                                                            regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES ||
                                                            i?.status ===
                                                            "coming_soon"
                                                          }
                                                          onClick={(e) => {
                                                            e.stopPropagation();
                                                            const unmatchedCodes =
                                                              i?.packages
                                                                .filter(
                                                                  (pkg) =>
                                                                    pkg.code !==
                                                                    user_package_check
                                                                )
                                                                .map(
                                                                  (pkg) =>
                                                                    pkg.code
                                                                );
                                                            setUnmatchedPackageCodes(
                                                              (prevCodes) => [
                                                                ...prevCodes,
                                                                ...unmatchedCodes,
                                                              ]
                                                            );
                                                            setPremiumGeneral(
                                                              true
                                                            );
                                                          }}
                                                          style={{
                                                            fontFamily:
                                                              "Archia Semibold",
                                                          }}
                                                        >
                                                          {/* King icon for premium modules */}
                                                          <img
                                                            src={King}
                                                            alt="Premium"
                                                            className="absolute -top-2 -right-2 w-5 h-5 z-10"
                                                          />
                                                          {false ? (
                                                            <span className="flex items-center justify-center">
                                                              <Loader
                                                                type="Oval"
                                                                color="white"
                                                                height={20}
                                                                width={20}
                                                              />
                                                              <span className="ml-2">
                                                                {t(
                                                                  "create_test.modules.adding"
                                                                )}
                                                              </span>
                                                            </span>
                                                          ) : (
                                                            t(
                                                              "create_test.modules.add_module"
                                                            )
                                                          )}
                                                        </button>
                                                      )
                                                    ) : (
                                                      // Third condition: packages array is empty
                                                      <button
                                                        className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                            MAX_REGULAR_MODULES
                                                            ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                            : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                          } text-sm font-medium rounded-md relative`}
                                                        disabled={
                                                          regularModulesOnly.length ===
                                                          MAX_REGULAR_MODULES || availableSlotsForRegular === 0
                                                        }
                                                        onClick={(e) => {
                                                          e.stopPropagation();
                                                          setStaticLoad(true);
                                                          handleAddModule(
                                                            i.id,
                                                            i.name,
                                                            i.time
                                                          );
                                                        }}
                                                        style={{
                                                          fontFamily:
                                                            "Archia Semibold",
                                                        }}
                                                      >
                                                        {false ? (
                                                          <span className="flex items-center justify-center">
                                                            <Loader
                                                              type="Oval"
                                                              color="white"
                                                              height={20}
                                                              width={20}
                                                            />
                                                            <span className="ml-2">
                                                              {t(
                                                                "create_test.modules.adding"
                                                              )}
                                                            </span>
                                                          </span>
                                                        ) : (
                                                          t(
                                                            "create_test.modules.add_module"
                                                          )
                                                        )}
                                                      </button>
                                                    )}
                                                    {regularModulesOnly.length ===
                                                      MAX_REGULAR_MODULES && (
                                                        <div className="tooltip2 w-[180px] text-center">
                                                          {t(
                                                            "create_test.modules.tooltips.cannot_add_more_than_5"
                                                          )}
                                                        </div>
                                                      )}
                                                    {i?.status ===
                                                      "coming_soon" && (
                                                        <div className="tooltip2 w-[180px] text-center">
                                                          {t(
                                                            "create_test.modules.tooltips.cannot_add_this_module"
                                                          )}
                                                        </div>
                                                      )}
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                </React.Fragment>
                              );
                            })}
                        </div>
                      </Scrollbars>
                    </div>
                    {/* {!data?.data?.other || data?.data?.other?.length === 0 || data?.data?.other.every(i => i.status === "pending") && ( */}
                    {data?.data?.other?.length === 0 && (
                      <div className="px-6 mt-3">
                        <div className="border border-[#FF5812] py-4 rounded">
                          <p
                            className="text-alertRed text-center"
                            style={{ fontFamily: "Silka" }}
                          >
                            {t("create_test.modules.no_modules_found.message")}{" "}
                            {""}
                            <span
                              style={{ fontFamily: "Silka Bold" }}
                              className="font-bold cursor-pointer"
                              onClick={handleEmailClick}
                            >
                              {t(
                                "create_test.modules.no_modules_found.clicking_here"
                              )}
                            </span>
                            .{" "}
                            {t(
                              "create_test.modules.no_modules_found.turnaround_time"
                            )}
                          </p>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <div
                      className={`mt-5 ${!companyData?.data?.relatedData?.length == 0 &&
                        "h-[100vh]"
                        } mb-5 enable-scrollbar2`}
                    >
                      <Scrollbars
                        autoHide
                        style={{ width: "100%", height: "90%" }}
                      >
                        <div
                          className="lg:grid-cols-2 xl:grid-cols-2 px-4 lg:pr-4 2xl:grid-cols-3 gap-4 md:grid-cols-2 grid sm:grid-cols-1"
                          ref={listInnerRef}
                          onScroll={onScroll}
                        >
                          {companyData?.data?.relatedData
                            ?.slice()
                            ?.sort((a, b) => a.name.localeCompare(b.name))
                            .map((i, j) => {
                              return (
                                <React.Fragment key={j}>
                                  {(i.status === "active" ||
                                    i?.status === "coming_soon") && (
                                      <div className="" key={j}>
                                        <div className="py-2">
                                          <div
                                            className={`bg-[#F6F7F7] px-5 border relative border-[#D3D5D8] corner-ribbon p-3 rounded-lg`}
                                          >
                                            {i?.status === "coming_soon" && (
                                              <div className="ribbon-container">
                                                <div className="ribbon">
                                                  <span class="ribbon-text">
                                                    {t(
                                                      "create_test.modules.coming_soon"
                                                    )}
                                                  </span>
                                                </div>
                                              </div>
                                            )}
                                            <div className="w-full grid grid-cols-10 md:gap-4 h-[80px]">
                                              <div className="col-span-7 my-auto">
                                                <h1
                                                  className={`text-lg font-bold good3 ${i?.status === "coming_soon" &&
                                                    "pl-10"
                                                    }  my-auto`}
                                                  style={{
                                                    fontFamily: "Archia Semibold",
                                                  }}
                                                >
                                                  {i.name}
                                                </h1>
                                              </div>
                                              <div
                                                className="my-auto col-span-3 ml-auto"
                                                style={{ fontFamily: "Silka" }}
                                              >
                                                {i?.experience === "Beginner" && (
                                                  <div className="inline-flex items-center border border-[#0B5B23] text-black py-1 px-3 rounded-full text-[12px]">
                                                    <span className="text-center">
                                                      {t(
                                                        "create_test.modules.experience_levels.beginner"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience ===
                                                  "Intermediate" && (
                                                    <div className="inline-flex items-center border border-[#FFB500] text-black  w-[90px] lg:w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                      <span className="text-center mx-auto">
                                                        {t(
                                                          "create_test.modules.experience_levels.intermediate"
                                                        )}
                                                      </span>
                                                    </div>
                                                  )}
                                                {i?.experience === "Advanced" && (
                                                  <div className="inline-flex items-center border border-[#FF5812] text-black  w-[90px] md:w-[100px] py-1 rounded-full text-[12px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.advanced"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                                {i?.experience === "General" && (
                                                  <div className="inline-flex items-center border border-coalColor text-black  xl:w-[72px] 2xl:w-[90px] md:w-[100px] py-1 rounded-full text-[12px] sm:w-[84px]">
                                                    <span className="text-center mx-auto">
                                                      {t(
                                                        "create_test.modules.experience_levels.all_levels"
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className="flex flex-row gap-2 mt-5">
                                              <img
                                                src={clock}
                                                className="w-4 h-4"
                                              />
                                              <p
                                                className="text-xs text-coalColor"
                                                style={{
                                                  fontFamily: "Silka Light",
                                                }}
                                              >
                                                {i?.time}{" "}
                                                {t("create_test.modules.mins")}
                                              </p>
                                            </div>
                                            <div className="h-[90px] pr-3 text-sm 2xl:mt-3 sm:mt-3 ">
                                              <div className="iosClamping h-[80px] overflow-hidden">
                                                <div
                                                  className={
                                                    styles["html-content"]
                                                  }
                                                >
                                                  {ReactHtmlParser(i.notes)}
                                                </div>
                                              </div>
                                              {i.notes.length > 100 && (
                                                <div
                                                  className="underline"
                                                  style={{
                                                    color: "#252E3A",
                                                    cursor: "pointer",
                                                    fontFamily: "Silka",
                                                  }}
                                                  onClick={() => {
                                                    setSectionID(i.id);
                                                    setDropdownOpen(true);
                                                  }}
                                                >
                                                  {" "}
                                                  {t(
                                                    "create_test.modules.read_more"
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                            <div className="grid grid-cols-2 mt-5">
                                              <div className="my-auto">
                                                {/* <a
                                                className="text-sm underline"
                                                style={{ fontFamily: "Silka" }}
                                                href={`/preview-module/${i.id}`}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                              >
                                                {t(
                                                  "create_test.modules.sample_questions"
                                                )}
                                              </a>{" "} */}
                                              </div>
                                              <div>
                                                {selectedModules.some(
                                                  (m) => m.id === i.id
                                                ) ? (
                                                  <button
                                                    className="inline-flex items-center w-full  px-4 py-3 justify-center border border-coalColor bg-[#C0FF06] text-coalColor text-sm font-bold rounded-md"
                                                    style={{
                                                      fontFamily: "Silka",
                                                    }}
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      handleDeleteModule(i.id);
                                                    }}
                                                  >
                                                    {t(
                                                      "create_test.modules.remove"
                                                    )}
                                                  </button>
                                                ) : (
                                                  <button
                                                    className={`inline-flex border  items-center justify-center px-4 w-full py-3 ${regularModulesOnly.length ===
                                                        MAX_REGULAR_MODULES ||
                                                        i?.status === "coming_soon"
                                                        ? "bg-[#D3D5D8] text-[#7C8289] disabled cursor-not-allowed"
                                                        : "border-coalColor bg-coalColor hover:bg-primaryGreen/90 text-white hover:text-coalColor"
                                                      } text-sm font-medium rounded-md relative`}
                                                    disabled={
                                                      regularModulesOnly.length ===
                                                      MAX_REGULAR_MODULES ||
                                                      i?.status === "coming_soon"
                                                    }
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      setStaticLoad(true);
                                                      handleAddModule(
                                                        i.id,
                                                        i.name,
                                                        i.time
                                                      );
                                                    }}
                                                    style={{
                                                      fontFamily:
                                                        "Archia Semibold",
                                                    }}
                                                  >
                                                    {false ? (
                                                      <span className="flex items-center justify-center">
                                                        <Loader
                                                          type="Oval"
                                                          color="white"
                                                          height={20}
                                                          width={20}
                                                        />
                                                        <span className="ml-2">
                                                          {t(
                                                            "create_test.modules.adding"
                                                          )}
                                                        </span>
                                                      </span>
                                                    ) : (
                                                      t(
                                                        "create_test.modules.add_module"
                                                      )
                                                    )}
                                                  </button>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                </React.Fragment>
                              );
                            })}
                        </div>
                      </Scrollbars>
                    </div>
                    {companyData?.data?.relatedData?.length === 0 && (
                      <div className="px-6 mt-3">
                        <div className="border border-[#FF5812] py-4 rounded">
                          <p
                            className="text-alertRed text-center"
                            style={{ fontFamily: "Silka" }}
                          >
                            No company modules found. Create custom modules to
                            see them here.
                          </p>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </React.Fragment>
            )}
          </div>
        </div>
        {dropdownOpen && (
          <div
            className="relative z-10"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div
              className="fixed inset-0 z-10 overflow-y-auto"
              onClick={() => setDropdownOpen(false)}
            >
              <div className="flex items-start justify-end  text-center">
                <div
                  className="relative transform overflow-x-scroll no-scrollbar bg-[#F8F8F8] text-left shadow-xl transition-all sm:w-full md:w-1/2"
                  onClick={handleButtonClick}
                >
                  <div className="bg-[#F8F8F8]  h-screen px-4 sm:px-6 no-scrollbar overflow-auto pb-8">
                    {isLoadingModule ? (
                      <div class="loader-container-1 col-span-6">
                        <div class="loader-1"></div>
                      </div>
                    ) : (
                      <>
                        <div
                          className="flex justify-end sticky top-0 "
                          style={{ paddingTop: "100px" }}
                        >
                          <img
                            src={close}
                            className="w-8 h-8 cursor-pointer"
                            onClick={() => setDropdownOpen(false)}
                          />
                        </div>
                        <div className="sm:px-0 md:px-0 lg:px-0 xl:px-10">
                          <div className="mt-10 flex sm:flex-col md:flex-col lg:flex-row md:gap-5">
                            {selectedModules.some(
                              (m) => m.id === ModuleData?.id
                            ) ? (
                              <button
                                className="inline-flex items-center px-4 py-3 bg-primaryGreen hover:bg-coalColor hover:text-white text-coalColor border border-coalColor text-sm font-medium rounded-md"
                                style={{ fontFamily: "Silka" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteModule(ModuleData?.id);
                                }}
                              >
                                <IoTrashBin
                                  alt="Add Transaction Icon"
                                  className="w-4 h-4 mr-4 icon-image "
                                />
                                {t("create_test.modules.remove")}
                              </button>
                            ) : (
                              <button
                                className="inline-flex items-center px-4  sm:mt-4 md:mt-0 py-3 bg-coalColor hover:bg-primaryGreen hover:text-black text-white border border-coalColor text-sm font-medium rounded-md"
                                style={{ fontFamily: "Silka" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setStaticLoad(true);
                                  handleAddModule(
                                    ModuleData?.id,
                                    ModuleData?.name,
                                    ModuleData?.time
                                  );
                                }}
                              >
                                <IoAddCircleOutline
                                  alt="Add Transaction Icon"
                                  className={`w-5 h-5 mr-4 icon-image`}
                                />
                                {false ? (
                                  <span className="flex items-center justify-center">
                                    <Loader
                                      type="Oval"
                                      color="white"
                                      height={20}
                                      width={20}
                                    />
                                    <span className="ml-2">
                                      {t("create_test.modules.adding")}
                                    </span>
                                  </span>
                                ) : (
                                  t("create_test.modules.add_module")
                                )}
                              </button>
                            )}
                            <button
                              className="inline-flex items-center px-4  sm:mt-4 md:mt-0 py-3 bg-coalColor hover:bg-primaryGreen hover:text-black text-white border border-coalColor text-sm font-medium rounded-md"
                              style={{ fontFamily: "Silka" }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleButtonClickPreview(ModuleData?.id);
                              }}
                            >
                              <FaRegEye
                                src={Preview}
                                alt="preview"
                                className={`w-5 h-5 mr-4 icon-image`}
                              />
                              {false ? (
                                <span className="flex items-center justify-center">
                                  <Loader
                                    type="Oval"
                                    color="white"
                                    height={20}
                                    width={20}
                                  />
                                  <span className="ml-2">
                                    {t("create_test.modules.adding")}
                                  </span>
                                </span>
                              ) : (
                                <>{t("tests.preview_module")}</>
                              )}
                            </button>
                          </div>
                          <div className="grid sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 bg-coalColor mt-10 rounded-lg">
                            <div className="p-2 pl-5 border-r py-5">
                              <img src={d1} className="w-8 h-8" />
                              <p className="text-lg text-white mt-1">
                                {ModuleData?.module_type === "multiple_choice"
                                  ? t("tests.multiple_choice")
                                  : t("tests.case_study")}
                              </p>
                              <p className="text-xs text-white">
                                {ModuleData?.category?.categoryName}
                              </p>
                            </div>
                            <div className="p-2 pl-5 border-r py-5">
                              <img src={d2} className="w-8 h-8" />
                              <p className="text-lg text-white mt-1">
                                {t("tests.time")}
                              </p>
                              <p className="text-xs text-white">
                                {ModuleData?.time} {t("tests.mins")}
                              </p>
                            </div>
                            <div className="p-2 pl-5 border-r py-5">
                              <img src={d3} className="w-8 h-8" />
                              <p className="text-lg text-white mt-1">
                                {t("tests.language")}
                              </p>
                              <p className="text-xs text-white">
                                {t("tests.english")}
                              </p>
                            </div>
                            <div className="p-2 pl-5 border-r py-5">
                              <img src={d4} className="w-8 h-8" />
                              <p className="text-lg text-white mt-1">
                                {t("tests.level")}
                              </p>
                              <p className="text-xs text-white">
                                {ModuleData?.experience === "General"
                                  ? t("tests.all_levels")
                                  : ModuleData?.experience}
                              </p>
                            </div>
                          </div>
                          <h1
                            className="mt-4 text-xl font-bold"
                            style={{ fontFamily: "Archia Bold" }}
                          >
                            {ModuleData?.name}
                          </h1>
                          <h1
                            className="mt-10 text-xl font-bold"
                            style={{ fontFamily: "Archia Bold" }}
                          >
                            {t("tests.covered_skills")}
                          </h1>
                          <div
                            className={styles["html-content"]}
                            style={{ fontFamily: "Silka" }}
                          >
                            {ReactHtmlParser(ModuleData?.covering_skills)}
                          </div>
                          <h1
                            className="mt-10 text-xl font-bold"
                            style={{ fontFamily: "Archia Bold" }}
                          >
                            {t("tests.relevant_for")}
                          </h1>
                          <div
                            className="text-[#767676]"
                            style={{ fontFamily: "Silka" }}
                          >
                            <div className={styles["html-content"]}>
                              {ReactHtmlParser(ModuleData?.test_relevent_for)}
                            </div>
                          </div>
                          <h1
                            className="mt-10 text-xl font-bold"
                            style={{ fontFamily: "Archia Bold" }}
                          >
                            {t("tests.description")}
                          </h1>
                          <div
                            className={styles["html-content"]}
                            style={{ fontFamily: "Silka" }}
                          >
                            {ReactHtmlParser(ModuleData?.notes)}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Modules;
