import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import queryString from "query-string";
import { useTranslation } from "react-i18next";
import { getCustomModulesByAssessment } from "../hooks/getCustomModulesByAssessment";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import { FaPlus } from "react-icons/fa6";
import { FaEdit } from "react-icons/fa";
import { FaTrash } from "react-icons/fa";
import { FaEye } from "react-icons/fa";
import DeleteModal from "../../../../../Components/ConfirmationModals/DeleteModal";
import { deleteSection } from "../hooks/deleteSection";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import PremiumGeneral from "../../../../../Components/Modals/PremiumGeneral";
import { MdModeEditOutline } from "react-icons/md";
import eye from "../../../../../Dexta_assets/eye.png";
import bin from "../../../../../Dexta_assets/bin.png";
import clock from "../../../../../Dexta_assets/clock.png";
import remove from "../../../../../Dexta_assets/close_icon.png";
import { Scrollbars } from "react-custom-scrollbars";
import CreateCustomTestAI from "./CreateCustomTestAI";
import useWindowSize from "../../../../../Helpers/useWindowSize";

const CustomModulesOverview = ({
  setselecteditem,
  setData,
  data,
  setQuestionLoading,
  onCreateNewModule,
  onModuleCountChange,
  onCustomModulesDataChange
}) => {
  const { t } = useTranslation();
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const assessment_id = localStorage.getItem("assessment_ID");
  const queryClient = useQueryClient();
  const user_package_check = useSelector(
    (state) => state.packageDetails.setPackage
  );
  const [premiumGeneralOpen, setPremiumGeneral] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [selectedModuleId, setSelectedModuleId] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [showAICreate, setShowAICreate] = useState(false);
  const size = useWindowSize();
  const isMobile = size.width <= 640;

  const currentAssessmentId = parsed && parsed.assessment_id !== undefined
    ? parsed.assessment_id
    : assessment_id;

  // Fetch custom modules for this assessment
  const {
    data: customModules,
    error: customModulesError,
    isLoading: customModulesLoading,
  } = useQuery(
    ["custom-modules", currentAssessmentId],
    () => getCustomModulesByAssessment(currentAssessmentId),
    {
      retry: false,
      enabled: !!currentAssessmentId && currentAssessmentId !== "null",
    }
  );

  // Delete module mutation
  const { mutate: deleteModule, isLoading: deleteModuleLoading } = useMutation(
    deleteSection,
    {
      onSuccess: () => {
        console.log('Delete module success callback, selectedModuleId:', selectedModuleId);
        console.log('Current selectedModules before removal:', data.selectedModules);

        setDeleteModal(false);

        // Immediately remove the module from selectedModules state
        if (selectedModuleId && data.selectedModules.some((m) => m.id === selectedModuleId)) {
          const newModules = data.selectedModules.filter((m) => m.id !== selectedModuleId);

          console.log('Delete success - removing module from selectedModules:', {
            oldModules: data.selectedModules,
            newModules,
            removedModuleId: selectedModuleId
          });

          // Update state immediately - this will trigger re-render and recalculation
          setData(prevData => ({
            ...prevData,
            selectedModules: newModules,
          }));
        }

        // Invalidate queries AFTER state update to ensure fresh data
        queryClient.invalidateQueries(["custom-modules", currentAssessmentId]);
        queryClient.invalidateQueries("assessment");

        setSelectedModuleId(null);
        // Decrement the custom module count immediately
        if (onModuleCountChange) {
          onModuleCountChange(prevCount => Math.max(0, prevCount - 1));
        }
        toast.success(t("create_test.questions.module_deleted_success"), {
          toastId: "module-deleted-success",
        });
      },
      onError: (error) => {
        toast.error(
          error.response?.data?.message || t("create_test.questions.module_delete_error"),
          { toastId: "module-delete-error" }
        );
      },
    }
  );

  const handleDeleteModule = (moduleId) => {
    setSelectedModuleId(moduleId);
    setDeleteModal(true);
  };

  const confirmDeleteModule = () => {
    if (selectedModuleId) {
      deleteModule(selectedModuleId);
    }
  };

  const handleCreateNewModule = () => {
    if (user_package_check !== "Enterprise") {
      setPremiumGeneral(true);
      return;
    }

    // Check if sidebar is full
    if (customModuleCount >= availableSlotsForCustom) {
      return; // Don't create if sidebar is full
    }

    // Show AI interface instead of calling onCreateNewModule directly
    setShowAICreate(true);
  };

  const handleGoBackFromAI = () => {
    setShowAICreate(false);
  };

  const handleNextStepFromAI = () => {
    // Handle next step logic
    console.log("Moving to next step from AI interface");
    // You can add navigation logic here
  };

  const handleAddQuestionFromAI = () => {
    // Handle adding question logic
    console.log("Adding question from AI interface");
    // You can add question creation logic here
  };

  const handleEditModule = (module) => {
    // Navigate to edit mode for this specific module
    setData({
      ...data,
      customQuestion: module.id,
      customQuestionDetail: module,
    });
    setselecteditem("questions");
    localStorage.setItem("current_module", "questions");
  };

  const handleViewModule = (moduleId) => {
    const url = `/preview/question/${moduleId}`;
    window.open(url, "_blank");
  };

  const handleAddModule = (id, name, time) => {
    if (data.selectedModules.some((m) => m.id === id)) return;
    const newModules = [
      ...data.selectedModules,
      { id, name, time: parseInt(time) || 0, isCustom: true }, // Mark as custom module
    ];
    setData({
      ...data,
      selectedModules: newModules,
    });
  };

  const handleRemoveModule = (id) => {

    // If this is a custom module, delete it from the assessment (which will also remove from selectedModules)
    const moduleToRemove = customModules?.customSections?.find(mod => mod.id === id);
    if (moduleToRemove) {
      console.log('Removing custom module:', moduleToRemove);
      handleDeleteModule(id);
    } else {
      console.log('Module not found in custom modules, removing from selectedModules only');
      // If it's not a custom module (shouldn't happen here, but just in case), just remove from selectedModules
      if (data.selectedModules.some((m) => m.id === id)) {
        const newModules = data.selectedModules.filter((m) => m.id !== id);
        setData({
          ...data,
          selectedModules: newModules,
        });
      }
    }
  };

  // Pass module count to parent component
  useEffect(() => {
    if (onModuleCountChange && customModules) {
      const moduleCount = customModules?.customSections?.length || 0;
      onModuleCountChange(moduleCount);
    }
  }, [customModules, onModuleCountChange]);

  // Helper: get selectedModules from props.data, fallback to []
  const selectedModules = data.selectedModules || [];

  // Pass custom modules data to parent component
  useEffect(() => {
    if (onCustomModulesDataChange && customModules) {
      const modulesData = customModules?.customSections || [];
      onCustomModulesDataChange(modulesData);
    }
  }, [customModules, onCustomModulesDataChange]);

  // Effect to automatically add all custom modules to selectedModules when they load
  // Since custom modules appearing on this page means they're already part of the assessment
  useEffect(() => {
    if (customModules?.customSections && customModules.customSections.length > 0) {
      let needsStateUpdate = false;
      const currentSelectedIds = selectedModules.map(m => m.id);
      const customModulesToAdd = [];

      // Check each custom module and add it to selectedModules if not already there
      customModules.customSections.forEach(customModule => {
        if (!currentSelectedIds.includes(customModule.id)) {
          customModulesToAdd.push({
            id: customModule.id,
            name: customModule.name,
            time: parseInt(customModule.time) || 0,
            isCustom: true
          });
          needsStateUpdate = true;
        }
      });

      // ONLY mark existing selected modules as custom if they match AND don't have isCustom flag
      const updatedModules = selectedModules.map(module => {
        const isCustomModule = customModules.customSections.some(customMod => customMod.id === module.id);
        // Only update if module doesn't have isCustom flag yet
        if (isCustomModule && !module.hasOwnProperty('isCustom')) {
          needsStateUpdate = true;
          return { ...module, isCustom: true };
        }
        return module;
      });

      // Update state only if needed
      if (needsStateUpdate) {
        const finalSelectedModules = [...updatedModules, ...customModulesToAdd];
        console.log('Auto-adding/marking custom modules:', {
          added: customModulesToAdd,
          updated: updatedModules.filter(m => m !== selectedModules.find(sm => sm.id === m.id)),
          final: finalSelectedModules
        });

        setData(prevData => ({
          ...prevData,
          selectedModules: finalSelectedModules,
        }));
      }
    }
  }, [customModules?.customSections, JSON.stringify(selectedModules.map(m => ({ id: m.id, isCustom: m.isCustom })))]); // Run when customModules loads OR selectedModules isCustom flags change

  // Separate effect to handle when selectedModules is first loaded from backend without isCustom flags
  useEffect(() => {
    // Only run if we have both customModules and selectedModules
    if (customModules?.customSections && selectedModules.length > 0) {
      let needsUpdate = false;

      const updatedModules = selectedModules
        .map(module => {
          const isCustomModule = customModules.customSections.some(customMod => customMod.id === module.id);

          // If module doesn't have isCustom property, set it based on whether it's in customModules
          if (!module.hasOwnProperty('isCustom')) {
            needsUpdate = true;
            return { ...module, isCustom: isCustomModule };
          }

          // If module has wrong isCustom flag, fix it
          if (isCustomModule && !module.isCustom) {
            needsUpdate = true;
            return { ...module, isCustom: true };
          }

          // CRITICAL FIX: If module is marked as custom but not in customModules, REMOVE it completely
          // This happens when a custom module is deleted - it should be removed, not converted to regular
          if (!isCustomModule && module.isCustom) {
            console.log('Removing deleted custom module from selectedModules:', module);
            needsUpdate = true;
            return null; // Mark for removal
          }

          return module;
        })
        .filter(module => module !== null); // Remove null entries

      if (needsUpdate) {
        console.log('Fixing/removing modules:', {
          before: selectedModules,
          after: updatedModules
        });
        setData(prevData => ({
          ...prevData,
          selectedModules: updatedModules,
        }));
      }
    }
  }, [customModules?.customSections?.length, selectedModules.length]); // Run when either loads

  // Calculate module limits
  const TOTAL_MODULE_LIMIT = 6; // Total modules allowed (regular + custom)
  const MAX_REGULAR_MODULES = 5; // Maximum regular modules allowed

  // Count regular modules (non-custom) and custom modules
  // Use isCustom flag as PRIMARY indicator, but add safety check for custom modules
  const regularModules = selectedModules.filter(module => {
    // Return true if module is NOT custom
    return !module.isCustom;
  });

  const customModulesSelected = selectedModules.filter(module => {
    // Return true only if module is custom AND still exists in backend
    if (!module.isCustom) return false;

    // Safety check: if customModules data is loaded, verify the module still exists
    if (customModules?.customSections && !customModulesLoading) {
      const stillExists = customModules.customSections.some(customMod => customMod.id === module.id);
      if (!stillExists) {
        console.log('Custom module no longer exists in backend, filtering out:', module);
        return false;
      }
    }

    return true;
  });

  const regularModuleCount = regularModules.length;
  const customModuleCount = customModulesSelected.length;
  const totalModulesSelected = selectedModules.length;

  // Calculate available slots for custom modules based on regular modules selected
  // Ensure we don't get negative values due to temporary state issues
  const availableSlotsForCustom = Math.max(0, TOTAL_MODULE_LIMIT - regularModuleCount);
  const canAddMoreCustomModules = customModuleCount < availableSlotsForCustom;

  // Temporary debug logging to track calculation
  console.log('Slots calculation:', {
    TOTAL_MODULE_LIMIT,
    regularModuleCount,
    customModuleCount,
    availableSlotsForCustom,
    canAddMoreCustomModules,
    timestamp: new Date().toISOString(),
    regularModules
  });



  if (customModulesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="loader-container-1">
          <div className="loader-1"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <PremiumGeneral
        premiumGeneralOpen={premiumGeneralOpen}
        setPremiumGeneral={setPremiumGeneral}
      />

      {deleteModal && (
        <DeleteModal
          setDeleteModal={setDeleteModal}
          onClick={confirmDeleteModule}
          icon={bin}
          header={t("create_test.questions.delete_modal.delete_module_header")}
          description={t("create_test.questions.delete_modal.delete_module_description")}
          buttonText={t("create_test.questions.delete_modal.delete_module_button")}
          loading={deleteModuleLoading}
        />
      )}

      {showAICreate ? (
        <CreateCustomTestAI
          onGoBack={handleGoBackFromAI}
          onNextStep={handleNextStepFromAI}
          onAddQuestion={handleAddQuestionFromAI}
        />
      ) : (
        <div>
          <div className="bg-white grid sm:grid-cols-1 lg:grid-cols-12 md:p-6">
            {/* Left Sidebar - Selected Custom Modules Only */}
            <div className="col-span-3 sm:border-none lg:border-r sm:p-6 md:p-0">
              <h2
                className="text-coalColor mt-3"
                style={{ fontFamily: "Archia Semibold" }}
              >
                Add modules
              </h2>
              <p
                className="mt-5 text-sm w-4/5"
                style={{ fontFamily: "Silka Light" }}
              >
                Your test can include up to 6 modules. Use different types of modules for the best results.
              </p>
              <p className="mt-8 text-sm" style={{ fontFamily: "Silka Light" }}>
                You have added:
              </p>

              <div className="flex mt-5 gap-3 flex-col w-full">
                {/* Show only selected custom modules */}
                {customModulesSelected.map((mod, index) => (
                  <div
                    className="sm:pr-0 lg:pr-5"
                    style={{ fontFamily: "Archia Semibold" }}
                    key={mod.id}
                  >
                    <button
                      type="button"
                      className={`text-coalColor relative flex good4 border border-[#252E3A] bg-[#C0FF06] focus:outline-none font-bold rounded-lg text-sm w-full align-center py-4 text-left overflow-hidden`}
                      onMouseEnter={() => setHoveredIndex(index)}
                      onMouseLeave={() => setHoveredIndex(null)}
                      title={mod.name}
                    >
                      <div className="flex items-center justify-center w-full relative">
                        <span className={`truncate px-5 w-5/6 mr-auto`}>
                          {mod.name || `Custom Module ${index + 1}`}
                        </span>
                        <div className="absolute top-0 right-2 ml-4 bottom-0 flex items-center justify-center pr-2">
                          <img
                            src={remove}
                            alt="Cross Icon"
                            className="w-5 h-5"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveModule(mod.id);
                            }}
                          />
                        </div>
                      </div>
                    </button>
                  </div>
                ))}

                {/* Show empty slots for remaining custom modules */}
                {[...Array(Math.max(availableSlotsForCustom - customModuleCount, 0))].map(
                  (_, index) => (
                    <div
                      className="sm:pr-0 lg:pr-5"
                      style={{ fontFamily: "Archia Semibold" }}
                      key={customModuleCount + index}
                    >
                      <button
                        type="button"
                        className={`text-[#7C8289] flex good4 border-2 border-[#D3D5D8] bg-[#F6F7F7] px-4 focus:outline-none rounded-lg text-sm w-full align-center py-4 justify-center text-left`}
                      >
                        {availableSlotsForCustom > 0
                          ? `Select custom module ${customModuleCount + index + 1}`
                          : "No custom slots available"
                        }
                      </button>
                    </div>
                  )
                )}
              </div>
            </div>

            {/* Right Side - Available Custom Modules */}
            <div className="col-span-9 lg:px-4">
              <div className="flex lg:justify-between sm:mt-10 lg:mt-0 lg:flex-row sm:flex-col">
                <div className="flex flex-row gap-5 px-2 my-auto">
                  <h2
                    className="text-coalColor rounded-md p-3 cursor-pointer"
                    style={{ fontFamily: "Archia Semibold" }}
                  >
                    Custom modules
                  </h2>
                </div>

                {/* Only show button when there are custom modules */}
                {customModules?.customSections?.length > 0 && (
                  <div className="flex sm:flex-row md:flex-row px-5 justify-between md:px-0 sm:gap-5 md:gap-5 items-center">
                    <div className="group relative">
                      <p
                        className={`underline text-sm my-auto md:text-base ${customModuleCount >= availableSlotsForCustom ? 'text-[#7C8289] cursor-not-allowed' : 'text-coalColor cursor-pointer'}`}
                        onClick={customModuleCount >= availableSlotsForCustom ? undefined : onCreateNewModule}
                        style={{ fontFamily: "Silka" }}
                      >
                        {isMobile ? "Add Custom Test Manually" : "Add Custom Test Manually"}
                      </p>
                      {customModuleCount >= availableSlotsForCustom ? (
                        <div className="tooltip2 w-[180px] text-center">
                          Maximum custom modules reached
                        </div>
                      ) : null}
                    </div>
                    <div className="sm:my-auto md:mt-4 lg:mt-0 group relative">
                      <CustomButton
                        // label={t("create_test.questions.create_new_module")}
                        label={`${isMobile ? "Create with AI" : "Create Custom Test with Dexta AI"}`}
                        bgColor={customModuleCount >= availableSlotsForCustom ? "#D3D5D8" : "#252E3A"}
                        paddingY="0.7rem"
                        textSize={`sm:text-sm md:text-base ${customModuleCount >= availableSlotsForCustom ? "text-[#7C8289]" : "text-[#FFFFFF]"}`}
                        borderCustom={customModuleCount >= availableSlotsForCustom ? "border border-gray-300" : "border border-black text-white"}
                        hoverBgColor={customModuleCount >= availableSlotsForCustom ? "#D3D5D8" : "#C0FF06"}
                        hoverTextColor={customModuleCount >= availableSlotsForCustom ? "#7C8289" : "#252E3A"}
                        onClickButton={handleCreateNewModule}
                        disabled={customModuleCount >= availableSlotsForCustom}
                        paddingx = "md:px-5 sm:px-2"
                      />
                      {customModuleCount >= availableSlotsForCustom ? (
                        <div className="tooltip2 w-[180px] text-center">
                          Maximum custom modules reached
                        </div>
                      ) : null}
                    </div>
                    {/* <div className="sm:mt-4 lg:mt-0 group relative">
                      <CustomButton
                        // label={t("create_test.questions.create_new_module")}
                        label="Create Custom Module"
                        bgColor={customModuleCount >= availableSlotsForCustom ? "#D3D5D8" : "#252E3A"}
                        paddingY="0.7rem"
                        textSize={`text-base ${customModuleCount >= availableSlotsForCustom ? "text-[#7C8289]" : "text-[#FFFFFF]"}`}
                        borderCustom={customModuleCount >= availableSlotsForCustom ? "border border-gray-300" : "border border-black text-white"}
                        hoverBgColor={customModuleCount >= availableSlotsForCustom ? "#D3D5D8" : "#C0FF06"}
                        hoverTextColor={customModuleCount >= availableSlotsForCustom ? "#7C8289" : "#252E3A"}
                        onClickButton={customModuleCount >= availableSlotsForCustom ? undefined : onCreateNewModule}
                        disabled={customModuleCount >= availableSlotsForCustom}
                      />
                      {customModuleCount >= availableSlotsForCustom ? (
                        <div className="tooltip2 w-[180px] text-center">
                          Maximum custom modules reached
                        </div>
                      ) : null}
                    </div> */}
                  </div>
                )}
              </div>
              {customModulesLoading ? (
                <div className="loader-container-1">
                  <div className="loader-1"></div>
                </div>
              ) : (
                <>
                  {(!customModules || customModules?.customSections?.length === 0) ? (
                    // Centered empty state matching the screenshot design
                    <div className="flex flex-col items-center justify-center h-[50vh] px-6">
                      <div className="text-center max-w-lg">
                        <h3
                          className="text-lg font-medium text-gray-900 mb-4"
                          style={{ fontFamily: "Silka" }}
                        >
                          You haven't created any custom modules yet. Start building your first module to tailor tests to your hiring needs.
                        </h3>

                        {/* <div className="mb-4 flex">
                          <div className="mx-auto" style={{ fontFamily: "Silka" }}>
                            <CustomButton
                              label="Create Custom Module"
                              bgColor="#252E3A"
                              textColor="#C0FF06"
                              paddingY="0.75rem"
                              textSize="text-base"
                              borderCustom="border border-black text-[#C0FF06]"
                              hoverBgColor="#C0FF06"
                              hoverTextColor="#252E3A"
                              paddingx="px-4"
                              onClickButton={customModuleCount >= availableSlotsForCustom ? undefined : onCreateNewModule}
                            />
                          </div>
                        </div> */}

                        {/* Primary Action Button */}
                        <div className="mb-4 flex">
                          <div className="mx-auto" style={{ fontFamily: "Silka" }}>
                            <CustomButton
                              label="Create Custom Test with Dexta AI"
                              bgColor="#252E3A"
                              textColor="#C0FF06"
                              paddingY="0.75rem"
                              textSize="text-base"
                              borderCustom="border border-black text-[#C0FF06]"
                              hoverBgColor="#C0FF06"
                              hoverTextColor="#252E3A"
                              paddingx="px-4"
                              onClickButton={handleCreateNewModule}
                            />
                          </div>
                        </div>

                        {/* OR Separator */}
                        <div className="mb-4">
                          <span
                            className="text-gray-500 text-sm"
                            style={{ fontFamily: "Silka Light" }}
                          >
                            OR
                          </span>
                        </div>

                        {/* Secondary Action Link */}
                        <div className="group relative">
                          <button
                            className={`text-sm underline transition-colors ${customModuleCount >= availableSlotsForCustom ? 'text-[#7C8289] cursor-not-allowed' : 'text-coalColor hover:font-bold'}`}
                            style={{ fontFamily: "Silka" }}
                            onClick={customModuleCount >= availableSlotsForCustom ? undefined : onCreateNewModule}
                          >
                            Create Custom Test Manually
                          </button>
                          {customModuleCount >= availableSlotsForCustom ? (
                            <div className="tooltip2 w-[180px] text-center">
                              Maximum custom modules reached
                            </div>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className={`mt-5 ${customModules?.customSections?.length > 0 && "h-[100vh]"} mb-5 enable-scrollbar2`}>
                      <Scrollbars
                        autoHide
                        style={{ width: "100%", height: "90%" }}
                      >
                        <div className="lg:grid-cols-2 xl:grid-cols-2 px-4 lg:pr-4 2xl:grid-cols-3 gap-4 md:grid-cols-2 grid sm:grid-cols-1">
                          {customModules?.customSections
                            ?.slice()
                            ?.sort((a, b) => a.name.localeCompare(b.name))
                            .map((module, index) => {
                              const isSelected = selectedModules.some((m) => m.id === module.id);

                              return (
                                <div className="" key={index}>
                                  <div className="py-2">
                                    <div className="bg-[#F6F7F7] px-5 border border-[#D3D5D8] p-3 rounded-lg">
                                      <div className="w-full grid grid-cols-10 md:gap-4 h-[80px]">
                                        <div className="col-span-7 my-auto">
                                          <div className="flex items-center gap-2">
                                            <h1
                                              className="text-lg font-bold good3 my-auto"
                                              style={{
                                                fontFamily: "Archia Semibold",
                                              }}
                                            >
                                              {module.name}
                                            </h1>
                                            {/* <button
                                              className="text-coalColor hover:text-blue-600 transition-colors p-1"
                                              onClick={() => handleEditModule(module)}
                                              title="Edit Module"
                                            >
                                              <MdModeEditOutline className="w-4 h-4" />
                                            </button> */}
                                          </div>
                                        </div>
                                      </div>

                                      <div className="flex flex-row gap-2 mt-5">
                                        <img
                                          src={clock}
                                          className="w-4 h-4"
                                        />
                                        <p
                                          className="text-xs text-coalColor"
                                          style={{
                                            fontFamily: "Silka Light",
                                          }}
                                        >
                                          {module?.time} {t("create_test.modules.mins")}
                                        </p>
                                      </div>

                                      <div className="grid grid-cols-2 mt-5">
                                        <div className="my-auto">
                                          <p
                                            className="text-sm underline cursor-pointer"
                                            style={{ fontFamily: "Silka" }}
                                            onClick={() => handleEditModule(module)}
                                          >
                                            Edit module
                                          </p>
                                        </div>
                                        <div>
                                          {/* Since all custom modules on this page are already in the assessment, show "Added" */}
                                          <button
                                            className="inline-flex items-center w-full px-4 py-3 justify-center border border-coalColor bg-[#C0FF06] text-coalColor text-sm font-bold rounded-md"
                                            style={{
                                              fontFamily: "Silka",
                                            }}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleRemoveModule(module.id);
                                            }}
                                          >
                                            Remove
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      </Scrollbars>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CustomModulesOverview; 